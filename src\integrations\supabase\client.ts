// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://nemvixtenuhyhsijditq.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5lbXZpeHRlbnVoeWhzaWpkaXRxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMDQ0OTYsImV4cCI6MjA2ODU4MDQ5Nn0.m7GGSOjSKSQsi4nKgUNNVk_RTEdVB4p0BkdlXbNdorY";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});
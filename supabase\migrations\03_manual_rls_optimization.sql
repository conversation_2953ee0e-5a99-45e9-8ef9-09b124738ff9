-- Manual RLS Optimization Migration
-- Run this manually if you prefer a more comprehensive approach
-- This addresses all Supabase linter warnings and adds additional optimizations

BEGIN;

-- Step 1: Drop all existing policies to start fresh
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Admins and managers can view all profiles" ON public.profiles;

-- Step 2: Create a materialized view for better performance (optional optimization)
-- This can help with complex role-based queries
CREATE OR REPLACE VIEW public.user_roles AS
SELECT 
  user_id,
  role,
  (select auth.uid()) as current_user_id,
  CASE 
    WHEN role IN ('admin', 'manager') THEN true 
    ELSE false 
  END as is_privileged_user
FROM public.profiles;

-- Step 3: Create optimized RLS policies

-- Single consolidated SELECT policy (fixes multiple permissive policies warning)
CREATE POLICY "profiles_select_policy" 
ON public.profiles 
FOR SELECT 
TO authenticated
USING (
  -- Current user can view their own profile
  user_id = (select auth.uid())
  OR
  -- Privileged users (admin/manager) can view all profiles
  (select auth.uid()) IN (
    SELECT user_id 
    FROM public.profiles 
    WHERE role IN ('admin', 'manager')
  )
);

-- Optimized UPDATE policy
CREATE POLICY "profiles_update_policy" 
ON public.profiles 
FOR UPDATE 
TO authenticated
USING (user_id = (select auth.uid()))
WITH CHECK (user_id = (select auth.uid()));

-- Optimized INSERT policy
CREATE POLICY "profiles_insert_policy" 
ON public.profiles 
FOR INSERT 
TO authenticated
WITH CHECK (user_id = (select auth.uid()));

-- Optional: DELETE policy for profile management
CREATE POLICY "profiles_delete_policy" 
ON public.profiles 
FOR DELETE 
TO authenticated
USING (
  user_id = (select auth.uid())
  OR
  -- Allow admins to delete any profile
  (select auth.uid()) IN (
    SELECT user_id 
    FROM public.profiles 
    WHERE role = 'admin'
  )
);

-- Step 4: Create helper functions for better performance

-- Function to check if current user has a specific role
CREATE OR REPLACE FUNCTION public.current_user_has_role(_role app_role)
RETURNS boolean
LANGUAGE SQL
STABLE
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.profiles
    WHERE user_id = (select auth.uid())
    AND role = _role
  );
$$;

-- Function to check if current user is privileged (admin or manager)
CREATE OR REPLACE FUNCTION public.current_user_is_privileged()
RETURNS boolean
LANGUAGE SQL
STABLE
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.profiles
    WHERE user_id = (select auth.uid())
    AND role IN ('admin', 'manager')
  );
$$;

-- Step 5: Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.current_user_has_role(app_role) TO authenticated;
GRANT EXECUTE ON FUNCTION public.current_user_is_privileged() TO authenticated;
GRANT SELECT ON public.user_roles TO authenticated;

-- Step 6: Add helpful comments
COMMENT ON POLICY "profiles_select_policy" ON public.profiles IS 
'Optimized single SELECT policy that consolidates user and admin access rules. Uses (select auth.uid()) for performance.';

COMMENT ON POLICY "profiles_update_policy" ON public.profiles IS 
'Optimized UPDATE policy using (select auth.uid()) to prevent function re-evaluation per row.';

COMMENT ON POLICY "profiles_insert_policy" ON public.profiles IS 
'Optimized INSERT policy using (select auth.uid()) to prevent function re-evaluation per row.';

COMMENT ON FUNCTION public.current_user_has_role(app_role) IS 
'Helper function to check if current user has a specific role. Optimized for RLS policies.';

COMMENT ON FUNCTION public.current_user_is_privileged() IS 
'Helper function to check if current user is admin or manager. Optimized for RLS policies.';

COMMIT;

-- Verification queries (run these to test the policies)
-- SELECT * FROM public.profiles; -- Should work based on your role
-- SELECT public.current_user_role(); -- Should return your role
-- SELECT public.current_user_is_privileged(); -- Should return true/false based on your role

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';

const Waiter = () => {
  const navigate = useNavigate();
  const { profile } = useAuth();

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold">Waiter Interface</h1>
            <p className="text-muted-foreground">Waiter ID: {profile?.user_id}</p>
          </div>
          <Button onClick={() => navigate('/')} variant="outline">
            Back to Dashboard
          </Button>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Create New Order</CardTitle>
              <CardDescription>Take a new order for a table</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Start a new order and add items from the menu.
              </p>
              <Button>New Order</Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>My Orders</CardTitle>
              <CardDescription>View orders assigned to you</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Manage and track your active orders.
              </p>
              <Button>View My Orders</Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Table Management</CardTitle>
              <CardDescription>Assign and manage table seating</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Assign customers to tables and manage seating.
              </p>
              <Button>Manage Tables</Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Menu Catalog</CardTitle>
              <CardDescription>Browse available menu items</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                View the current menu and item availability.
              </p>
              <Button>View Menu</Button>
            </CardContent>
          </Card>
        </div>

        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Orders you create will be linked to your unique user ID: <strong>{profile?.user_id}</strong>
            </p>
            <p className="text-sm text-muted-foreground">
              This allows cashiers to access and process your orders for billing.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Waiter;
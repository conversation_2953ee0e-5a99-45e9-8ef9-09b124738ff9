# RLS Policy Optimization Guide

## Problem Summary
Your Supabase database has performance warnings related to Row Level Security (RLS) policies:

1. **Auth RLS Initialization Plan**: `auth.uid()` calls are being re-evaluated for each row
2. **Multiple Permissive Policies**: Multiple SELECT policies exist for the same table/role combination

## Solutions Created

### Option 1: Basic Optimization (`02_optimize_rls_policies.sql`)
- Fixes all linter warnings
- Consolidates multiple SELECT policies into one
- Uses `(select auth.uid())` to prevent re-evaluation
- Minimal changes, maintains existing functionality

### Option 2: Comprehensive Optimization (`03_manual_rls_optimization.sql`)
- All fixes from Option 1
- Additional helper functions for better performance
- Optional DELETE policy for admin management
- Helper view for complex queries
- More comprehensive approach

## How to Apply

### Method 1: Using Supabase CLI (Recommended)
```bash
# Navigate to your project directory
cd c:\Users\<USER>\Downloads\table-side-pos-system

# Apply the basic optimization
supabase db push

# Or apply manually
supabase db reset
```

### Method 2: Manual Application via Supabase Dashboard
1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor**
3. Copy and paste the contents of `02_optimize_rls_policies.sql`
4. Click **Run** to execute

### Method 3: Direct SQL Execution
```sql
-- Copy and paste the entire content of either migration file
-- into your SQL editor and execute
```

## What Gets Fixed

### Before (Performance Issues):
```sql
-- ❌ Re-evaluates auth.uid() for each row
USING (auth.uid() = user_id)

-- ❌ Multiple policies for same action
CREATE POLICY "Users can view their own profile" ...
CREATE POLICY "Admins and managers can view all profiles" ...
```

### After (Optimized):
```sql
-- ✅ Evaluates auth.uid() once per query
USING ((select auth.uid()) = user_id)

-- ✅ Single consolidated policy
CREATE POLICY "Optimized profile view policy" 
USING (
  (select auth.uid()) = user_id
  OR
  EXISTS (SELECT 1 FROM public.profiles p WHERE p.user_id = (select auth.uid()) AND p.role IN ('admin', 'manager'))
);
```

## Performance Benefits

1. **Reduced Function Calls**: `auth.uid()` evaluated once per query instead of per row
2. **Simplified Policy Evaluation**: Single policy instead of multiple policies
3. **Better Query Planning**: PostgreSQL can optimize queries more effectively
4. **Scalability**: Performance improvements become more significant with larger datasets

## Testing After Migration

Run these queries to verify everything works:

```sql
-- Test basic profile access
SELECT * FROM public.profiles;

-- Test role-based access (if you have the helper functions)
SELECT public.current_user_role();
SELECT public.current_user_is_privileged();

-- Verify policies are active
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'profiles';
```

## Rollback (If Needed)

If you need to rollback, you can restore the original policies:

```sql
-- Drop optimized policies
DROP POLICY IF EXISTS "Optimized profile view policy" ON public.profiles;
DROP POLICY IF EXISTS "Optimized profile update policy" ON public.profiles;
DROP POLICY IF EXISTS "Optimized profile insert policy" ON public.profiles;

-- Recreate original policies (from 01_createuser.sql)
-- [Include original policy creation statements]
```

## Next Steps

1. Apply one of the migration files
2. Run the verification queries
3. Monitor performance improvements
4. Consider applying similar optimizations to other tables if you add them

The optimizations maintain all existing functionality while significantly improving performance at scale.

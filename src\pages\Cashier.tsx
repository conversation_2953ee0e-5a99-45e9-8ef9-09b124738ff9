import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

const Cashier = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Cashier Station</h1>
          <Button onClick={() => navigate('/')} variant="outline">
            Back to Dashboard
          </Button>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Process Payments</CardTitle>
              <CardDescription>Handle customer payments and billing</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Process cash, card, and digital payments for orders.
              </p>
              <Button>Payment Terminal</Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>View Orders</CardTitle>
              <CardDescription>Access all waiter orders for billing</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                View orders created by waiters and process billing.
              </p>
              <Button>View All Orders</Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Generate Bills</CardTitle>
              <CardDescription>Create and print customer bills</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Generate itemized bills and receipts for customers.
              </p>
              <Button>Generate Bill</Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Daily Sales</CardTitle>
              <CardDescription>View today's sales summary</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Monitor daily transactions and payment summary.
              </p>
              <Button>Sales Summary</Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Cashier;
-- Drop existing policies that have performance issues
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Admins and managers can view all profiles" ON public.profiles;

-- Create optimized policies with (select auth.uid()) to prevent re-evaluation

-- Consolidated SELECT policy: Users can view their own profile OR admins/managers can view all profiles
CREATE POLICY "Optimized profile view policy" 
ON public.profiles 
FOR SELECT 
TO authenticated
USING (
  -- User can view their own profile
  (select auth.uid()) = user_id
  OR
  -- Admins and managers can view all profiles
  EXISTS (
    SELECT 1 FROM public.profiles p
    WHERE p.user_id = (select auth.uid())
    AND p.role IN ('admin', 'manager')
  )
);

-- Optimized UPDATE policy: Users can update their own profile
CREATE POLICY "Optimized profile update policy" 
ON public.profiles 
FOR UPDATE 
TO authenticated
USING ((select auth.uid()) = user_id)
WITH CHECK ((select auth.uid()) = user_id);

-- Optimized INSERT policy: Users can insert their own profile
CREATE POLICY "Optimized profile insert policy" 
ON public.profiles 
FOR INSERT 
TO authenticated
WITH CHECK ((select auth.uid()) = user_id);

-- Optimized DELETE policy: Users can delete their own profile (optional, add if needed)
-- CREATE POLICY "Optimized profile delete policy" 
-- ON public.profiles 
-- FOR DELETE 
-- TO authenticated
-- USING ((select auth.uid()) = user_id);

-- Create an optimized function for role checking that can be used in other policies
CREATE OR REPLACE FUNCTION public.current_user_role()
RETURNS app_role
LANGUAGE SQL
STABLE
SECURITY DEFINER
AS $$
  SELECT role
  FROM public.profiles
  WHERE user_id = (select auth.uid())
  LIMIT 1;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.current_user_role() TO authenticated;

-- Comment explaining the optimizations
COMMENT ON POLICY "Optimized profile view policy" ON public.profiles IS 
'Consolidated policy that allows users to view their own profile and admins/managers to view all profiles. Uses (select auth.uid()) to prevent re-evaluation per row.';

COMMENT ON POLICY "Optimized profile update policy" ON public.profiles IS 
'Optimized policy using (select auth.uid()) to prevent re-evaluation per row for update operations.';

COMMENT ON POLICY "Optimized profile insert policy" ON public.profiles IS 
'Optimized policy using (select auth.uid()) to prevent re-evaluation per row for insert operations.';

COMMENT ON FUNCTION public.current_user_role() IS 
'Optimized function to get current user role without re-evaluation. Can be used in other policies for better performance.';

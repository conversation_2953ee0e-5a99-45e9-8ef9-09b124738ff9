import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useNavigate } from 'react-router-dom';

const Dashboard = () => {
  const { profile, signOut } = useAuth();
  const navigate = useNavigate();

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin':
        return 'destructive';
      case 'manager':
        return 'default';
      case 'cashier':
        return 'secondary';
      case 'waiter':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getAvailableRoutes = (role: string) => {
    const routes = [];
    
    switch (role) {
      case 'admin':
        routes.push(
          { path: '/admin', label: 'Admin Panel', description: 'Manage users and system settings' },
          { path: '/manager', label: 'Manager Dashboard', description: 'View analytics and manage menu' },
          { path: '/cashier', label: 'Cashier Station', description: 'Process payments and billing' },
          { path: '/waiter', label: 'Waiter Interface', description: 'Take orders and manage tables' }
        );
        break;
      case 'manager':
        routes.push(
          { path: '/manager', label: 'Manager Dashboard', description: 'View analytics and manage menu' },
          { path: '/cashier', label: 'Cashier Station', description: 'Process payments and billing' },
          { path: '/waiter', label: 'Waiter Interface', description: 'Take orders and manage tables' }
        );
        break;
      case 'cashier':
        routes.push(
          { path: '/cashier', label: 'Cashier Station', description: 'Process payments and billing' }
        );
        break;
      case 'waiter':
        routes.push(
          { path: '/waiter', label: 'Waiter Interface', description: 'Take orders and manage tables' }
        );
        break;
    }
    
    return routes;
  };

  if (!profile) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">Loading profile...</div>
      </div>
    );
  }

  const availableRoutes = getAvailableRoutes(profile.role);

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-start mb-8">
          <div>
            <h1 className="text-3xl font-bold mb-2">Restaurant POS Dashboard</h1>
            <p className="text-muted-foreground">Welcome back, {profile.full_name || profile.email}</p>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant={getRoleBadgeVariant(profile.role)} className="text-sm">
              {profile.role.toUpperCase()}
            </Badge>
            <Button onClick={signOut} variant="outline">
              Sign Out
            </Button>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {availableRoutes.map((route) => (
            <Card key={route.path} className="cursor-pointer hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-lg">{route.label}</CardTitle>
                <CardDescription>{route.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <Button 
                  onClick={() => navigate(route.path)} 
                  className="w-full"
                >
                  Access {route.label}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <Card className="mt-8">
          <CardHeader>
            <CardTitle>User Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <p><strong>Name:</strong> {profile.full_name || 'Not set'}</p>
            <p><strong>Email:</strong> {profile.email}</p>
            <p><strong>Role:</strong> {profile.role}</p>
            <p><strong>User ID:</strong> {profile.user_id}</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
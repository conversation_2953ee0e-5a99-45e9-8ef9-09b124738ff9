-- Complete Database Setup with Optimized RLS Policies
-- This migration combines all database setup and optimizations in one file
-- Fixes all Supabase linter warnings and provides comprehensive functionality

BEGIN;

-- ============================================================================
-- STEP 1: CREATE TYPES AND TABLES
-- ============================================================================

-- Create user roles enum
CREATE TYPE public.app_role AS ENUM ('admin', 'manager', 'cashier', 'waiter');

-- Create profiles table for additional user information
CREATE TABLE public.profiles (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL UNIQUE REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name TEXT,
  email TEXT,
  role app_role NOT NULL DEFAULT 'waiter',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 2: CREATE OPTIMIZED RLS POLICIES (FIXES LINTER WARNINGS)
-- ============================================================================

-- Single consolidated SELECT policy (fixes multiple permissive policies warning)
-- Uses (select auth.uid()) to prevent re-evaluation per row
CREATE POLICY "profiles_select_policy" 
ON public.profiles 
FOR SELECT 
TO authenticated
USING (
  -- Current user can view their own profile
  user_id = (select auth.uid())
  OR
  -- Privileged users (admin/manager) can view all profiles
  (select auth.uid()) IN (
    SELECT user_id 
    FROM public.profiles 
    WHERE role IN ('admin', 'manager')
  )
);

-- Optimized UPDATE policy
CREATE POLICY "profiles_update_policy" 
ON public.profiles 
FOR UPDATE 
TO authenticated
USING (user_id = (select auth.uid()))
WITH CHECK (user_id = (select auth.uid()));

-- Optimized INSERT policy
CREATE POLICY "profiles_insert_policy" 
ON public.profiles 
FOR INSERT 
TO authenticated
WITH CHECK (user_id = (select auth.uid()));

-- Optional: DELETE policy for profile management
CREATE POLICY "profiles_delete_policy" 
ON public.profiles 
FOR DELETE 
TO authenticated
USING (
  user_id = (select auth.uid())
  OR
  -- Allow admins to delete any profile
  (select auth.uid()) IN (
    SELECT user_id 
    FROM public.profiles 
    WHERE role = 'admin'
  )
);

-- ============================================================================
-- STEP 3: CREATE UTILITY FUNCTIONS
-- ============================================================================

-- Function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
NEW.updated_at = now();
RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
BEGIN
  INSERT INTO public.profiles (user_id, full_name, email, role)
  VALUES (
    NEW.id, 
    COALESCE(NEW.raw_user_meta_data ->> 'full_name', ''),
    NEW.email,
    COALESCE((NEW.raw_user_meta_data ->> 'role')::app_role, 'waiter')
  );
  RETURN NEW;
END;
$$;

-- Function to check user role (security definer to bypass RLS)
CREATE OR REPLACE FUNCTION public.has_role(_user_id UUID, _role app_role)
RETURNS BOOLEAN
LANGUAGE SQL
STABLE
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.profiles
    WHERE user_id = _user_id
      AND role = _role
  )
$$;

-- Optimized function to get current user role
CREATE OR REPLACE FUNCTION public.current_user_role()
RETURNS app_role
LANGUAGE SQL
STABLE
SECURITY DEFINER
AS $$
  SELECT role
  FROM public.profiles
  WHERE user_id = (select auth.uid())
  LIMIT 1;
$$;

-- Function to check if current user has a specific role
CREATE OR REPLACE FUNCTION public.current_user_has_role(_role app_role)
RETURNS boolean
LANGUAGE SQL
STABLE
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.profiles
    WHERE user_id = (select auth.uid())
    AND role = _role
  );
$$;

-- Function to check if current user is privileged (admin or manager)
CREATE OR REPLACE FUNCTION public.current_user_is_privileged()
RETURNS boolean
LANGUAGE SQL
STABLE
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.profiles
    WHERE user_id = (select auth.uid())
    AND role IN ('admin', 'manager')
  );
$$;

-- ============================================================================
-- STEP 4: CREATE TRIGGERS
-- ============================================================================

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_profiles_updated_at
BEFORE UPDATE ON public.profiles
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Trigger to create profile when user signs up
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- ============================================================================
-- STEP 5: CREATE VIEWS FOR BETTER PERFORMANCE
-- ============================================================================

-- Create a view for better performance with complex role-based queries
CREATE OR REPLACE VIEW public.user_roles AS
SELECT 
  user_id,
  role,
  (select auth.uid()) as current_user_id,
  CASE 
    WHEN role IN ('admin', 'manager') THEN true 
    ELSE false 
  END as is_privileged_user
FROM public.profiles;

-- ============================================================================
-- STEP 6: GRANT PERMISSIONS
-- ============================================================================

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION public.current_user_role() TO authenticated;
GRANT EXECUTE ON FUNCTION public.current_user_has_role(app_role) TO authenticated;
GRANT EXECUTE ON FUNCTION public.current_user_is_privileged() TO authenticated;
GRANT SELECT ON public.user_roles TO authenticated;

-- ============================================================================
-- STEP 7: ADD DOCUMENTATION COMMENTS
-- ============================================================================

COMMENT ON POLICY "profiles_select_policy" ON public.profiles IS 
'Optimized single SELECT policy that consolidates user and admin access rules. Uses (select auth.uid()) for performance.';

COMMENT ON POLICY "profiles_update_policy" ON public.profiles IS 
'Optimized UPDATE policy using (select auth.uid()) to prevent function re-evaluation per row.';

COMMENT ON POLICY "profiles_insert_policy" ON public.profiles IS 
'Optimized INSERT policy using (select auth.uid()) to prevent function re-evaluation per row.';

COMMENT ON POLICY "profiles_delete_policy" ON public.profiles IS 
'Optimized DELETE policy allowing users to delete own profile and admins to delete any profile.';

COMMENT ON FUNCTION public.current_user_role() IS 
'Optimized function to get current user role without re-evaluation. Can be used in other policies for better performance.';

COMMENT ON FUNCTION public.current_user_has_role(app_role) IS 
'Helper function to check if current user has a specific role. Optimized for RLS policies.';

COMMENT ON FUNCTION public.current_user_is_privileged() IS 
'Helper function to check if current user is admin or manager. Optimized for RLS policies.';

COMMENT ON VIEW public.user_roles IS 
'Performance-optimized view for role-based queries and access control.';

COMMIT;

-- ============================================================================
-- VERIFICATION QUERIES (UNCOMMENT TO TEST)
-- ============================================================================

-- Test basic functionality:
-- SELECT * FROM public.profiles;
-- SELECT public.current_user_role();
-- SELECT public.current_user_is_privileged();

-- Verify policies are active:
-- SELECT schemaname, tablename, policyname, permissive, roles, cmd 
-- FROM pg_policies WHERE tablename = 'profiles';
